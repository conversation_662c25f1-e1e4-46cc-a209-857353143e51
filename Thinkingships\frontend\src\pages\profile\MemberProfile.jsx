import { useState } from 'react';
import { BiGlobe } from 'react-icons/bi';
import { FaEnvelope, FaFacebook, FaInstagram, FaLinkedin, FaTwitter, FaUserPlus } from 'react-icons/fa';
import { SiGoodreads } from 'react-icons/si';

function MemberProfile({ user = null }) {
  const [currentView, setCurrentView] = useState('info'); // 'info', 'skrivee', 'feedback'
  const [activeContentTab, setActiveContentTab] = useState('Stories'); // 'Stories', 'Poems', 'Blogs', 'e-books'
  const profileData = {
    username: 'LitByNandini',
    fullName: '<PERSON><PERSON><PERSON>',
    rank: 1800,
    fans: 150,
    faves: 640,
    bio: 'I am a passionate writer with a keen eye for detail, dedicated to exploring the complexities of the human experience through captivating storytelling that leaves a lasting impression.',
    passion: 'Lorem ipsum dolor sit amet consectetur. Enim rhoncus blandit consequat vel nulla at feugiat volutpat.',
    gender: 'Female',
    dob: '07-03-1991',
    mobile: '+012345678',
    occupation: 'Student',
    email: '<EMAIL>',
    location: 'New Delhi',
    language: 'English',
    social: {
      instagram: 'nandini.kumar',
      facebook: 'nandini.kumar',
      linkedin: 'nandini.kumar',
      twitter: 'nandini.kumar',
      goodreads: 'nandini.kumar',
    }
  };

  // Sample content data for Nandini's posts
  const memberContent = {
    Stories: [
      {
        id: 1,
        title: "Can We Survive The New Campaign Of Age Of Darkness Final Stand",
        author: "Nandini Kumar",
        date: "Oct 27, 2020",
        genre: "Romance",
        tags: ["#isseehat", "#mysteries", "#adventure", "#adventure"],
        excerpt: "Lorem ipsum dolor sit amet consectetur. Eros commodo accumsan ullam oper imperdiet sed ullamcorper dolor. Eget quis fermentum vestibulum ac odio in risque sed sed ex. Suspendisse tellus. Gravida ultrices tempus varius blandit suscipit augue netus odio ornare cursus nibh vestibulum.",
        likes: 245,
        views: 1200,
        comments: 34,
        image: "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
      },
      {
        id: 2,
        title: "The Midnight Garden's Secret",
        author: "Nandini Kumar",
        date: "Sep 15, 2020",
        genre: "Mystery",
        tags: ["#mystery", "#thriller", "#suspense"],
        excerpt: "In the heart of an old mansion lies a garden that blooms only at midnight. Sarah discovers this enchanted place holds secrets that could change her life forever.",
        likes: 189,
        views: 890,
        comments: 22,
        image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
      }
    ],
    Poems: [
      {
        id: 3,
        title: "Whispers of the Heart",
        author: "Nandini Kumar",
        date: "Nov 12, 2020",
        genre: "Poetry",
        tags: ["#poetry", "#love", "#emotions"],
        excerpt: "In silence deep, where shadows play, The heart speaks truths we cannot say. Through whispered winds and moonlit streams, It weaves the fabric of our dreams.",
        likes: 156,
        views: 678,
        comments: 18,
        image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
      }
    ],
    Blogs: [
      {
        id: 4,
        title: "The Art of Storytelling in Modern Literature",
        author: "Nandini Kumar",
        date: "Dec 3, 2020",
        genre: "Blog",
        tags: ["#writing", "#literature", "#tips"],
        excerpt: "Exploring the evolution of storytelling techniques and how modern authors are reshaping narrative structures to captivate contemporary readers.",
        likes: 203,
        views: 1450,
        comments: 41,
        image: "https://images.unsplash.com/photo-1455390582262-044cdead277a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
      }
    ],
    "e-books": [
      {
        id: 5,
        title: "Digital Hearts: A Collection",
        author: "Nandini Kumar",
        date: "Jan 8, 2021",
        genre: "E-book",
        tags: ["#ebook", "#collection", "#romance"],
        excerpt: "A comprehensive collection of romantic short stories that explore love in the digital age, featuring interconnected characters and modern relationships.",
        likes: 312,
        views: 2100,
        comments: 67,
        image: "https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
      }
    ]
  };

  return (
    <div className="flex-1 w-full bg-gradient-to-br from-indigo-50 via-white to-cyan-50 min-h-screen relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-cyan-400/10 to-blue-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-purple-400/5 to-pink-400/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Profile Header */}
      <div className="bg-white/90 backdrop-blur-md p-4 sm:p-6 lg:p-8 border-b border-gray-200/50 text-center shadow-2xl relative overflow-hidden">
        {/* Enhanced Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/40 to-purple-50/40"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/20 to-transparent"></div>
        <div className="relative z-10">
          <div className="flex justify-center gap-8 sm:gap-12 lg:gap-16 items-center mb-4 sm:mb-6">
            {/* Fans */}
            <div className="group cursor-pointer relative">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-100/50 to-purple-100/50 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-500 transform scale-95 group-hover:scale-100"></div>
              <div className="relative z-10 p-4 rounded-2xl">
                <div className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent group-hover:from-blue-500 group-hover:to-purple-500 transition-all duration-300 group-hover:scale-110">
                  {profileData.fans}
                </div>
                <div className="text-xs sm:text-sm text-gray-600 font-semibold tracking-wider">FANS</div>
                <div className="w-8 sm:w-10 lg:w-12 h-1 bg-gradient-to-r from-[#4A99F8] to-purple-500 mx-auto mt-2 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 shadow-lg"></div>
              </div>
            </div>

            {/* Circular Profile Image */}
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-all duration-500 animate-pulse"></div>
              <div className="w-20 h-20 sm:w-24 sm:h-24 lg:w-32 lg:h-32 rounded-full overflow-hidden border-4 border-white shadow-2xl group-hover:shadow-3xl transition-all duration-500 group-hover:scale-105 relative z-10">
                <img
                  src={user?.profileImage || "https://img.daisyui.com/images/profile/demo/<EMAIL>"}
                  alt={profileData.fullName}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
              </div>
              <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-[#4A99F8]/30 to-purple-500/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute -inset-2 bg-gradient-to-r from-blue-400/20 to-purple-500/20 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>

            {/* Faves */}
            <div className="group cursor-pointer relative">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/50 to-pink-100/50 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-500 transform scale-95 group-hover:scale-100"></div>
              <div className="relative z-10 p-4 rounded-2xl">
                <div className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent group-hover:from-purple-500 group-hover:to-pink-500 transition-all duration-300 group-hover:scale-110">
                  {profileData.faves}
                </div>
                <div className="text-xs sm:text-sm text-gray-600 font-semibold tracking-wider">FAVES</div>
                <div className="w-8 sm:w-10 lg:w-12 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto mt-2 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 shadow-lg"></div>
              </div>
            </div>
          </div>

          {/* Username + Rank */}
          <div className="text-xs sm:text-sm text-gray-500 mb-1 sm:mb-2 font-medium">Rank {profileData.rank}</div>
          <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-4 sm:mb-6 text-gray-800 hover:text-[#4A99F8] transition-colors duration-300">{profileData.username}</h2>

          {/* Buttons */}
          <div className="flex flex-col sm:flex-row justify-center gap-4 sm:gap-6 mt-6 sm:mt-8 mb-8 sm:mb-10 px-4 sm:px-0">
            <button className="group relative flex items-center justify-center gap-3 bg-[#4A99F8] hover:bg-[#0A06F4] text-white px-8 sm:px-10 py-3 sm:py-4 rounded-full text-sm font-bold shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-500 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <FaUserPlus size={16} className="group-hover:rotate-12 transition-transform duration-500 relative z-10" />
              <span className="relative z-10">Follow</span>
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400/30 to-blue-500/30 blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </button>
            <button className="group relative flex items-center justify-center gap-3 bg-[#4A99F8] hover:bg-[#0A06F4] text-white px-8 sm:px-10 py-3 sm:py-4 rounded-full text-sm font-bold shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-500 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <FaEnvelope size={16} className="group-hover:scale-110 transition-transform duration-500 relative z-10" />
              <span className="relative z-10">Message</span>
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400/30 to-blue-500/30 blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </button>
          </div>

          {/* Bio */}
          <p className="text-gray-700 text-xs sm:text-sm max-w-xs sm:max-w-2xl lg:max-w-3xl mx-auto mb-6 sm:mb-8 leading-relaxed bg-white/50 backdrop-blur-sm p-3 sm:p-4 rounded-lg shadow-sm border border-gray-100">
            {profileData.bio}
          </p>

          {/* Tabs */}
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 sm:gap-6 max-w-xs sm:max-w-md lg:max-w-lg mx-auto px-4 sm:px-0">
            <button
              onClick={() => setCurrentView('info')}
              className={`relative group ${currentView === 'info' ? 'bg-[#0A06F4]' : 'bg-[#4A99F8]'} hover:bg-[#0A06F4] text-white px-6 py-3 rounded-xl text-sm font-bold shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-500 min-w-[100px] overflow-hidden`}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <span className="relative z-10">INFO</span>
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-400/30 to-blue-500/30 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </button>
            <button
              onClick={() => setCurrentView('skrivee')}
              className={`relative group ${currentView === 'skrivee' ? 'bg-[#0A06F4]' : 'bg-[#4A99F8]'} hover:bg-[#0A06F4] text-white px-6 py-3 rounded-xl text-sm font-bold shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-500 min-w-[100px] overflow-hidden`}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <span className="relative z-10">SKRIVEE</span>
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-400/30 to-blue-500/30 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </button>
            <button
              onClick={() => setCurrentView('feedback')}
              className={`relative group ${currentView === 'feedback' ? 'bg-[#0A06F4]' : 'bg-[#4A99F8]'} hover:bg-[#0A06F4] text-white px-6 py-3 rounded-xl text-sm font-bold shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-500 min-w-[100px] overflow-hidden`}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <span className="relative z-10">FEEDBACK</span>
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-400/30 to-blue-500/30 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex justify-center p-4 sm:p-6 lg:p-8">
        {currentView === 'info' && (
          /* Info Card */
          <div className="bg-white/95 backdrop-blur-lg rounded-3xl border border-gray-200/50 p-6 sm:p-8 lg:p-10 w-full max-w-xs sm:max-w-lg lg:max-w-2xl xl:max-w-3xl shadow-2xl hover:shadow-3xl transition-all duration-700 relative overflow-hidden group">
          {/* Enhanced Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-purple-50/20 to-pink-50/40 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
          <div className="absolute top-0 left-0 w-full h-1 bg-[#4A99F8]"></div>
          <div className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-2xl"></div>
          <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-tr from-purple-400/10 to-pink-400/10 rounded-full blur-2xl"></div>

          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6 sm:mb-8">
              <h3 className="font-bold text-lg sm:text-xl lg:text-2xl text-gray-800 hover:text-[#4A99F8] transition-colors duration-300">{profileData.fullName}</h3>
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#2E3A59] to-[#4A99F8] rounded-full flex items-center justify-center text-white shadow-lg hover:shadow-xl transform hover:scale-110 hover:rotate-12 transition-all duration-300 cursor-pointer">
                <BiGlobe size={16} className="sm:w-5 sm:h-5" />
              </div>
            </div>

            <div className="space-y-4 sm:space-y-6 text-xs sm:text-sm">
              <div className="group">
                <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 text-sm sm:text-base group-hover:text-[#4A99F8] transition-colors duration-300">Passion</strong>
                <p className="text-gray-700 leading-relaxed bg-gradient-to-r from-blue-50 to-purple-50 p-3 sm:p-4 rounded-lg border-l-4 border-[#4A99F8] hover:shadow-md transition-all duration-300">{profileData.passion}</p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                  <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Gender</strong>
                  <p className="text-gray-700 font-medium">{profileData.gender}</p>
                </div>
                <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                  <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Date of Birth</strong>
                  <p className="text-gray-700 font-medium">{profileData.dob}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                  <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Mobile Number</strong>
                  <p className="text-gray-700 font-medium break-all">{profileData.mobile}</p>
                </div>
                <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                  <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Occupation</strong>
                  <p className="text-gray-700 font-medium">{profileData.occupation}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                  <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Email Id</strong>
                  <p className="text-gray-700 font-medium break-all">{profileData.email}</p>
                </div>
                <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                  <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Location</strong>
                  <p className="text-gray-700 font-medium">{profileData.location}</p>
                </div>
              </div>

              <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Language</strong>
                <p className="text-gray-700 font-medium">{profileData.language}</p>
              </div>
            </div>
          </div>
        </div>
        )}

        {currentView === 'skrivee' && (
          <div className="w-full max-w-xs sm:max-w-lg lg:max-w-2xl xl:max-w-3xl mx-auto">
            {/* Blue Background Line - Same width as upper content box */}
            <div className="w-full bg-blue-500 py-1 shadow-lg">
              <div className="px-4">
                <div className="flex justify-evenly items-center w-full">
                  <button
                    onClick={() => setActiveContentTab('Stories')}
                    className={`px-6 py-2 rounded-lg font-bold flex items-center gap-2 transition-all duration-300 min-w-[120px] justify-center ${
                      activeContentTab === 'Stories'
                        ? 'bg-white text-blue-600 shadow-xl scale-110 transform'
                        : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105 hover:shadow-lg'
                    }`}
                  >
                    <span className="text-lg">📚</span>
                    <span className="text-base">Stories</span>
                  </button>

                  <button
                    onClick={() => setActiveContentTab('Poems')}
                    className={`px-6 py-2 rounded-lg font-bold flex items-center gap-2 transition-all duration-300 min-w-[120px] justify-center ${
                      activeContentTab === 'Poems'
                        ? 'bg-white text-blue-600 shadow-xl scale-110 transform'
                        : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105 hover:shadow-lg'
                    }`}
                  >
                    <span className="text-lg">🎵</span>
                    <span className="text-base">Poems</span>
                  </button>

                  <button
                    onClick={() => setActiveContentTab('Blogs')}
                    className={`px-6 py-2 rounded-lg font-bold flex items-center gap-2 transition-all duration-300 min-w-[120px] justify-center ${
                      activeContentTab === 'Blogs'
                        ? 'bg-white text-blue-600 shadow-xl scale-110 transform'
                        : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105 hover:shadow-lg'
                    }`}
                  >
                    <span className="text-lg">📝</span>
                    <span className="text-base">Blogs</span>
                  </button>

                  <button
                    onClick={() => setActiveContentTab('e-books')}
                    className={`px-6 py-2 rounded-lg font-bold flex items-center gap-2 transition-all duration-300 min-w-[120px] justify-center ${
                      activeContentTab === 'e-books'
                        ? 'bg-white text-blue-600 shadow-xl scale-110 transform'
                        : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105 hover:shadow-lg'
                    }`}
                  >
                    <span className="text-lg">📖</span>
                    <span className="text-base">e-books</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Main Content Container with Scrolling */}
            <div className="bg-gradient-to-br from-white via-blue-50 to-indigo-100 h-96 relative overflow-hidden">
              {/* Enhanced Background Patterns */}
              <div className="absolute inset-0 z-0 opacity-10"></div>

              {/* Scrollable Content Container */}
              <div className="h-full overflow-y-auto px-4 py-4">
                {/* Sort Options */}
                <div className="flex justify-end mb-6">
                  <div className="bg-white/90 backdrop-blur-md rounded-xl p-2 shadow-lg">
                    <span className="text-sm text-gray-600 mr-2">Sort by</span>
                    <select className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg px-3 py-1.5 text-gray-800 font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm">
                      <option value="Most Read">Most Read</option>
                      <option value="Newest">Newest</option>
                      <option value="Oldest">Oldest</option>
                      <option value="Most Liked">Most Liked</option>
                    </select>
                  </div>
                </div>

                {/* Content Items - Exact same as Authors Page */}
                <div className="space-y-8">
                {memberContent[activeContentTab]?.map((item) => (
                  <div
                    key={item.id}
                    className="group bg-white/95 backdrop-blur-md border border-gray-200/50 rounded-3xl p-8 shadow-2xl hover:shadow-3xl transition-all duration-700 relative overflow-hidden transform hover:-translate-y-2 hover:scale-[1.02]"
                  >
                    {/* Advanced Decorative Background Elements */}
                    <div className="absolute -right-16 -top-16 w-32 h-32 bg-gradient-to-br from-blue-200/30 to-indigo-200/30 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-pulse blur-xl"></div>
                    <div className="absolute -left-16 -bottom-16 w-32 h-32 bg-gradient-to-br from-purple-200/30 to-pink-200/30 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700 delay-100 animate-pulse blur-xl"></div>

                    {/* Floating Particles */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-60 transition-opacity duration-1000">
                      <div className="absolute top-8 right-8 w-2 h-2 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full animate-ping shadow-lg"></div>
                      <div className="absolute top-16 left-16 w-1.5 h-1.5 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-ping delay-500 shadow-lg"></div>
                      <div className="absolute bottom-16 right-16 w-2 h-2 bg-gradient-to-r from-indigo-400 to-blue-400 rounded-full animate-ping delay-1000 shadow-lg"></div>
                    </div>

                    {/* Content Container */}
                    <div className="relative z-10">
                      {/* Author Header - Enhanced */}
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center gap-4">
                          <img
                            src={user?.profileImage || "https://img.daisyui.com/images/profile/demo/<EMAIL>"}
                            alt={item.author}
                            className="w-12 h-12 rounded-full border-2 border-blue-300 shadow-md object-cover"
                          />
                          <span className="font-bold text-gray-800 text-lg group-hover:text-blue-700 transition-colors duration-300">{item.author}</span>
                          <span className="text-blue-600 text-xs font-medium bg-blue-50 px-2 py-1 rounded-full ml-2">Author</span>
                        </div>
                        <button className="bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white px-8 py-3 rounded-2xl font-semibold transition-all duration-500 transform hover:scale-110 hover:-translate-y-1 shadow-xl hover:shadow-blue-500/50">
                          Follow
                        </button>
                      </div>

                      {/* Content Title - Enhanced */}
                      <h2 className="text-xl font-bold text-gray-800 mb-4 leading-tight group-hover:text-blue-700 transition-colors duration-300">
                        {item.title}
                      </h2>

                      {/* Category and Date - Enhanced */}
                      <div className="flex items-center gap-4 mb-4">
                        <span className="bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium border border-blue-100 shadow-sm">
                          {item.genre}
                        </span>
                        <span className="bg-gradient-to-r from-gray-50 to-slate-100 text-gray-700 px-3 py-1 rounded-full text-sm border border-gray-200 shadow-sm">
                          {item.date}
                        </span>
                      </div>

                      {/* Tags - Enhanced */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {item.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="text-blue-600 text-sm hover:text-blue-800 cursor-pointer transition-colors duration-300"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>

                      {/* Excerpt - Enhanced */}
                      <p className="text-gray-600 text-sm leading-relaxed mb-4 group-hover:text-gray-700 transition-colors duration-300">
                        {item.excerpt}
                      </p>

                      {/* Content Image - Enhanced */}
                      <div className="relative rounded-2xl overflow-hidden shadow-lg group-hover:shadow-xl transition-all duration-500 transform group-hover:scale-[1.02]">
                        <img
                          src={item.image}
                          alt="Content preview"
                          className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-105"
                          onError={(e) => {
                            // First fallback - try a different image
                            if (!e.target.dataset.fallback) {
                              e.target.dataset.fallback = "1";
                              const fallbackImages = {
                                'Romance': 'https://picsum.photos/800/400?random=1',
                                'Mystery': 'https://picsum.photos/800/400?random=2',
                                'Fantasy': 'https://picsum.photos/800/400?random=3',
                                'Science': 'https://picsum.photos/800/400?random=4'
                              };
                              e.target.src = fallbackImages[item.genre] || 'https://picsum.photos/800/400?random=5';
                            } else {
                              // Final fallback - colored placeholder
                              e.target.src = `https://via.placeholder.com/800x400/3B82F6/FFFFFF?text=${encodeURIComponent(item.genre)}`;
                            }
                          }}
                          loading="lazy"
                        />
                      </div>

                      {/* Action Icons */}
                      <div className="mt-4 flex justify-around items-center border-t border-gray-200 pt-4">
                        <button className="flex items-center gap-2 text-gray-600 hover:text-blue-500 transition-colors duration-300">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                          </svg>
                          <span className="font-semibold text-sm">Like</span>
                        </button>
                        <button className="flex items-center gap-2 text-gray-600 hover:text-green-500 transition-colors duration-300">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                          <span className="font-semibold text-sm">Comment</span>
                        </button>
                        <button className="flex items-center gap-2 text-gray-600 hover:text-purple-500 transition-colors duration-300">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.368a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                          </svg>
                          <span className="font-semibold text-sm">Share</span>
                        </button>
                        <button className="flex items-center gap-2 text-gray-600 hover:text-orange-500 transition-colors duration-300">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                          </svg>
                          <span className="font-semibold text-sm">Save</span>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {currentView === 'feedback' && (
          <div className="h-96 overflow-y-auto bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 rounded-2xl">
            <div className="p-6 sm:p-8">
              <div className="text-center mb-8">
                <h3 className="text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
                  Testimonials
                </h3>
                <p className="text-lg text-gray-600 font-medium">{profileData.name}</p>
                <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mt-3 rounded-full"></div>
              </div>

              {/* Write a Testimonial Section */}
              <div className="mb-10 bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h4 className="text-xl font-bold text-gray-800">Write a Testimonial</h4>
                </div>
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-4 border border-blue-100">
                  <textarea
                    placeholder="Share your thoughts about this amazing writer..."
                    className="w-full h-28 p-4 border-0 bg-white/70 backdrop-blur-sm rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-400 focus:bg-white transition-all duration-300 placeholder-gray-500"
                  />
                </div>
                <div className="flex justify-end">
                  <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105">
                    <span className="flex items-center gap-2">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"/>
                      </svg>
                      Post Testimonial
                    </span>
                  </button>
                </div>
              </div>

              {/* Sort Options */}
              <div className="flex justify-between items-center mb-8">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zM3 16a1 1 0 011-1h4a1 1 0 110 2H4a1 1 0 01-1-1z"/>
                    </svg>
                  </div>
                  <span className="text-gray-700 font-semibold">All Testimonials</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-gray-600 font-medium">Sort by</span>
                  <select className="bg-white/80 backdrop-blur-sm border border-blue-200 rounded-xl px-4 py-2 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent shadow-sm">
                    <option>Popular</option>
                    <option>Recent</option>
                    <option>Oldest</option>
                  </select>
                </div>
              </div>

              {/* Testimonials List */}
              <div className="space-y-8">
                {/* Testimonial 1 */}
                <div className="bg-white/90 backdrop-blur-sm border border-white/50 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.02] group">
                  <div className="flex items-start gap-5">
                    <div className="relative">
                      <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                        <span className="text-white font-bold text-lg">WW</span>
                      </div>
                      <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white"></div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3 flex-wrap">
                        <h5 className="font-bold text-gray-800 text-lg">Wade Warren</h5>
                        <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-md">
                          ⭐ Rank 1270
                        </span>
                        <button className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-1.5 rounded-full text-xs font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105">
                          + Follow
                        </button>
                        <button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1.5 rounded-full text-xs font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105">
                          💬 Message
                        </button>
                        <button className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-all duration-200">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                          </svg>
                        </button>
                      </div>
                      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-4 border-l-4 border-blue-400">
                        <p className="text-gray-700 leading-relaxed font-medium">
                          "I recently had the pleasure of reading an incredible story that captivated me from beginning to end. The author's vivid imagination, compelling characters, and intriguing plot all came together to create an unforgettable experience. The story was expertly crafted, with each twist and turn leaving me on the edge..."
                        </p>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"/>
                          </svg>
                          <span className="text-gray-500 text-sm font-medium">March 6, 2018</span>
                        </div>
                        <button className="text-blue-600 text-sm font-semibold hover:text-blue-700 hover:underline transition-all duration-200">
                          Read More →
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Testimonial 2 */}
                <div className="bg-white/90 backdrop-blur-sm border border-white/50 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.02] group">
                  <div className="flex items-start gap-5">
                    <div className="relative">
                      <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                        <span className="text-white font-bold text-lg">JB</span>
                      </div>
                      <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white"></div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3 flex-wrap">
                        <h5 className="font-bold text-gray-800 text-lg">Jerome Bell</h5>
                        <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-md">
                          ⭐ Rank 1270
                        </span>
                        <button className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-1.5 rounded-full text-xs font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105">
                          + Follow
                        </button>
                        <button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1.5 rounded-full text-xs font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105">
                          💬 Message
                        </button>
                        <button className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-all duration-200">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                          </svg>
                        </button>
                      </div>
                      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-4 border-l-4 border-blue-400">
                        <p className="text-gray-700 leading-relaxed font-medium">
                          "I recently had the pleasure of reading an incredible story that captivated me from beginning to end. The author's vivid imagination, compelling characters, and intriguing plot all came together to create an unforgettable experience. The story was expertly crafted, with each twist and turn leaving me on the edge..."
                        </p>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"/>
                          </svg>
                          <span className="text-gray-500 text-sm font-medium">March 6, 2018</span>
                        </div>
                        <button className="text-blue-600 text-sm font-semibold hover:text-blue-700 hover:underline transition-all duration-200">
                          Read More →
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Testimonial 3 */}
                <div className="bg-white/90 backdrop-blur-sm border border-white/50 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.02] group">
                  <div className="flex items-start gap-5">
                    <div className="relative">
                      <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                        <span className="text-white font-bold text-lg">AS</span>
                      </div>
                      <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white"></div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3 flex-wrap">
                        <h5 className="font-bold text-gray-800 text-lg">Arlene Simmons</h5>
                        <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-md">
                          ⭐ Rank 890
                        </span>
                        <button className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-1.5 rounded-full text-xs font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105">
                          + Follow
                        </button>
                        <button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1.5 rounded-full text-xs font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105">
                          💬 Message
                        </button>
                        <button className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-all duration-200">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                          </svg>
                        </button>
                      </div>
                      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-4 border-l-4 border-blue-400">
                        <p className="text-gray-700 leading-relaxed font-medium">
                          "Outstanding work! The depth of character development and the intricate plot weaving made this an absolutely delightful read. Every chapter brought new surprises and kept me thoroughly engaged throughout the entire journey."
                        </p>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"/>
                          </svg>
                          <span className="text-gray-500 text-sm font-medium">February 15, 2018</span>
                        </div>
                        <button className="text-blue-600 text-sm font-semibold hover:text-blue-700 hover:underline transition-all duration-200">
                          Read More →
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Social Links - Only show when currentView is 'info' */}
      {currentView === 'info' && (
        <div className="bg-white/95 backdrop-blur-lg rounded-3xl border border-gray-200/50 p-6 sm:p-8 lg:p-10 mt-8 sm:mt-10 mx-4 sm:mx-6 lg:mx-8 shadow-2xl hover:shadow-3xl transition-all duration-700 relative overflow-hidden group">
          {/* Enhanced Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-purple-50/20 to-pink-50/40 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
          <div className="absolute top-0 left-0 w-full h-1 bg-[#4A99F8]"></div>
          <div className="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-2xl"></div>
          <div className="absolute -bottom-16 -left-16 w-32 h-32 bg-gradient-to-tr from-blue-400/10 to-purple-400/10 rounded-full blur-2xl"></div>

          <div className="relative z-10">
            <h3 className="text-lg sm:text-xl font-bold text-gray-800 mb-4 sm:mb-6 text-center">Connect With Me</h3>
            <div className="space-y-3 sm:space-y-4">
              {[
                {
                  icon: <FaInstagram size={16} className="sm:w-5 sm:h-5" />,
                  color: 'bg-gradient-to-br from-purple-500 to-pink-500',
                  label: profileData.social.instagram,
                  hoverColor: 'hover:from-purple-600 hover:to-pink-600'
                },
                {
                  icon: <FaFacebook size={16} className="sm:w-5 sm:h-5" />,
                  color: 'bg-blue-600',
                  label: profileData.social.facebook,
                  hoverColor: 'hover:bg-blue-700'
                },
                {
                  icon: <FaLinkedin size={16} className="sm:w-5 sm:h-5" />,
                  color: 'bg-blue-700',
                  label: profileData.social.linkedin,
                  hoverColor: 'hover:bg-blue-800'
                },
                {
                  icon: <FaTwitter size={16} className="sm:w-5 sm:h-5" />,
                  color: 'bg-blue-400',
                  label: profileData.social.twitter,
                  hoverColor: 'hover:bg-blue-500'
                },
                {
                  icon: <SiGoodreads size={16} className="sm:w-5 sm:h-5" />,
                  color: 'bg-amber-700',
                  label: profileData.social.goodreads,
                  hoverColor: 'hover:bg-amber-800'
                },
              ].map(({ icon, color, label, hoverColor }, idx) => (
                <div key={idx} className="group/item flex items-center gap-3 sm:gap-4 lg:gap-5 p-3 sm:p-4 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 rounded-lg sm:rounded-xl transition-all duration-300 cursor-pointer hover:shadow-lg transform hover:scale-105">
                  <div className={`w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 ${color} ${hoverColor} rounded-full flex items-center justify-center text-white shadow-lg group-hover/item:shadow-xl transition-all duration-300 group-hover/item:scale-110 group-hover/item:rotate-12`}>
                    {icon}
                  </div>
                  <span className="text-gray-700 text-sm sm:text-base font-semibold group-hover/item:text-[#4A99F8] transition-colors duration-300 truncate flex-1">{label}</span>
                  <div className="ml-auto opacity-0 group-hover/item:opacity-100 transition-opacity duration-300 hidden sm:block">
                    <svg className="w-4 h-4 sm:w-5 sm:h-5 text-[#4A99F8]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default MemberProfile;
