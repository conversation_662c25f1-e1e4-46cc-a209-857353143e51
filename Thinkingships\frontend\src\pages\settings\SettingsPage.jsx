import { useState } from 'react';

function SettingsPage() {
  const [activeTab, setActiveTab] = useState('account');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Settings Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Settings</h1>
        </div>

        {/* Settings Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {/* Account Card */}
          <div 
            onClick={() => setActiveTab('account')}
            className={`p-6 rounded-2xl border-2 cursor-pointer transition-all duration-300 ${
              activeTab === 'account' 
                ? 'bg-blue-500 text-white border-blue-500 shadow-lg' 
                : 'bg-white text-gray-700 border-gray-200 hover:border-blue-300 hover:shadow-md'
            }`}
          >
            <h3 className="text-xl font-bold mb-3">Account</h3>
            <p className={`text-sm ${activeTab === 'account' ? 'text-blue-100' : 'text-gray-600'}`}>
              Manage your account details, change your email or password and delete your account if needed.
            </p>
          </div>

          {/* Notifications Card */}
          <div 
            onClick={() => setActiveTab('notifications')}
            className={`p-6 rounded-2xl border-2 cursor-pointer transition-all duration-300 ${
              activeTab === 'notifications' 
                ? 'bg-blue-500 text-white border-blue-500 shadow-lg' 
                : 'bg-white text-gray-700 border-gray-200 hover:border-blue-300 hover:shadow-md'
            }`}
          >
            <h3 className="text-xl font-bold mb-3">Notifications</h3>
            <p className={`text-sm ${activeTab === 'notifications' ? 'text-blue-100' : 'text-gray-600'}`}>
              Customize your notification preferences for messages, content engagement and other updates.
            </p>
          </div>

          {/* Payment & Subscription Card */}
          <div 
            onClick={() => setActiveTab('payment')}
            className={`p-6 rounded-2xl border-2 cursor-pointer transition-all duration-300 ${
              activeTab === 'payment' 
                ? 'bg-blue-500 text-white border-blue-500 shadow-lg' 
                : 'bg-white text-gray-700 border-gray-200 hover:border-blue-300 hover:shadow-md'
            }`}
          >
            <h3 className="text-xl font-bold mb-3">Payment & Subscription</h3>
            <p className={`text-sm ${activeTab === 'payment' ? 'text-blue-100' : 'text-gray-600'}`}>
              View and manage your subscription plan, payment methods and earnings.
            </p>
          </div>

          {/* Feedback and Support Card */}
          <div 
            onClick={() => setActiveTab('support')}
            className={`p-6 rounded-2xl border-2 cursor-pointer transition-all duration-300 ${
              activeTab === 'support' 
                ? 'bg-blue-500 text-white border-blue-500 shadow-lg' 
                : 'bg-white text-gray-700 border-gray-200 hover:border-blue-300 hover:shadow-md'
            }`}
          >
            <h3 className="text-xl font-bold mb-3">Feedback and Support</h3>
            <p className={`text-sm ${activeTab === 'support' ? 'text-blue-100' : 'text-gray-600'}`}>
              Report issues, contact support and access helpful resources.
            </p>
          </div>
        </div>

        {/* Account Settings Section */}
        {activeTab === 'account' && (
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-8">Account Settings</h2>
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left Column - Account Details */}
              <div className="lg:col-span-2 space-y-6">
                {/* Email Update */}
                <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <label className="text-lg font-semibold text-gray-800">Email id*</label>
                    <button className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                      Update
                    </button>
                  </div>
                  <input
                    type="email"
                    defaultValue="<EMAIL>"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                {/* Current Password */}
                <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <label className="text-lg font-semibold text-gray-800">Current Password*</label>
                    <button className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                      Update
                    </button>
                  </div>
                  <input
                    type="password"
                    defaultValue="••••••••••••••••••"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                {/* New Password */}
                <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                  <label className="block text-lg font-semibold text-gray-800 mb-4">New Password</label>
                  <input
                    type="password"
                    placeholder="••••••••••••••••••"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                {/* Confirm New Password */}
                <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <label className="text-lg font-semibold text-gray-800">Confirm New Password</label>
                    <button className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                      Save
                    </button>
                  </div>
                  <input
                    type="password"
                    placeholder="••••••••••••••••••"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Right Column - Delete Account */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                  <h3 className="text-xl font-bold text-gray-800 mb-6">Delete Account</h3>
                  
                  <div className="space-y-4 mb-6">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">Email id*</label>
                      <input
                        type="email"
                        defaultValue="<EMAIL>"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">Current Password*</label>
                      <input
                        type="password"
                        placeholder="••••••••••••••••••"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div className="mb-6">
                    <p className="text-sm text-red-600 font-medium">
                      This Action Cannot Be Undone. All Your Data Will Be Permanently Deleted.
                    </p>
                  </div>

                  <button className="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    Confirm
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Other tabs content placeholders */}
        {activeTab === 'notifications' && (
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Notification Settings</h2>
            <p className="text-gray-600">Notification settings will be implemented here.</p>
          </div>
        )}

        {activeTab === 'payment' && (
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Payment & Subscription</h2>
            <p className="text-gray-600">Payment and subscription settings will be implemented here.</p>
          </div>
        )}

        {activeTab === 'support' && (
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Feedback and Support</h2>
            <p className="text-gray-600">Support and feedback options will be implemented here.</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default SettingsPage;
